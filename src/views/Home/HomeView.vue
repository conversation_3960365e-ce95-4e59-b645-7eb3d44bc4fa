<template>
  <MainLayout>
    <BFHomeView v-if="bizCode === 'fupin'"/>
    <ZyHomeView v-if="bizCode === 'ziying'"/>
    <WelfareHomeView v-if="bizCode === 'welfaresop'"/>
    <LaborHomeView v-if="bizCode === 'labor'"/>
    <YgjdHomeView v-if="bizCode === 'ygjd'"/>
    <ZQHomeView v-if="bizCode === 'zq'"/>
    <LnzxHomeView v-if="bizCode === 'lnzx'"/>
    <SfznHomeView v-if="bizCode === 'sfzn'"/>
  </MainLayout>
</template>

<script setup>
import { ref } from 'vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import BFHomeView from '@views/Home/BFHome/BFHomeView.vue'
import { getBizCode } from '@utils/curEnv.js'
import ZyHomeView from '@views/Home/ZYHome/ZYHomeView.vue'
import WelfareHomeView from '@views/Home/WelfareHome/WelfareHomeView.vue'
import LaborHomeView from '@views/Home/LaborHome/LaborHomeView.vue'
import ZQHomeView from '@views/Home/ZQHome/ZQHomeView.vue'
import LnzxHomeView from '@views/Home/LnxzHome/LnzxHomeView.vue'
import YgjdHomeView from "@views/Home/YgjdHome/YgjdHomeView.vue";
import SfznHomeView from '@views/Home/SfznHome/SfznHomeView.vue'
const bizCode = ref(getBizCode())
</script>

<style lang="less" scoped>

</style>
